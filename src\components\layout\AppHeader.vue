<template>
  <header class="bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-20">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-3 group">
            <div class="relative">
              <div
                class="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105"
              >
                <svg
                  class="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
                  ></path>
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
                  ></path>
                </svg>
              </div>
              <div
                class="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full flex items-center justify-center"
              >
                <span class="text-white text-xs">🔥</span>
              </div>
            </div>
            <div>
              <span
                class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                >Kigali GasGo</span
              >
              <div class="text-xs text-gray-500 font-medium">Fast Gas Delivery</div>
            </div>
          </router-link>
        </div>

        <!-- Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <template v-if="!authStore.isAuthenticated">
            <router-link
              to="/"
              class="text-gray-600 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              Home
            </router-link>
            <router-link
              to="/login"
              class="text-gray-600 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              Login
            </router-link>
            <router-link
              to="/signup"
              class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              Sign Up
            </router-link>
          </template>

          <template v-else>
            <!-- Role-based navigation -->
            <template v-if="authStore.userRole === 'BUYER'">
              <router-link
                to="/buyer/dashboard"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Browse Gas
              </router-link>
              <router-link
                to="/buyer/orders"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                My Orders
              </router-link>
            </template>

            <template v-if="authStore.userRole === 'SELLER'">
              <router-link
                to="/seller/dashboard"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Dashboard
              </router-link>
              <router-link
                to="/seller/inventory"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Inventory
              </router-link>
              <router-link
                to="/seller/orders"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Orders
              </router-link>
            </template>

            <template v-if="authStore.userRole === 'ADMIN'">
              <router-link
                to="/admin/dashboard"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Dashboard
              </router-link>
              <router-link
                to="/admin/orders"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Orders
              </router-link>
              <router-link
                to="/admin/invoices"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Invoices
              </router-link>
              <router-link
                to="/admin/users"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Users
              </router-link>
              <router-link
                to="/admin/reports"
                class="text-gray-600 hover:text-gray-900 transition-colors"
              >
                Reports
              </router-link>
            </template>

            <!-- User menu -->
            <div class="relative" ref="userMenuRef">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium">{{ userInitials }}</span>
                </div>
                <span class="hidden sm:block">{{
                  getUserDisplayName()
                }}</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
              >
                <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-200">
                  <div class="font-medium">{{ getUserDisplayName() }}</div>
                  <div class="text-gray-500">{{ authStore.user?.user?.email }}</div>
                  <div class="text-xs text-primary-600 capitalize">{{ authStore.userRole?.toLowerCase() }}</div>
                </div>
                <button
                  @click="handleLogout"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  Sign out
                </button>
              </div>
            </div>
          </template>
        </nav>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div v-if="showMobileMenu" class="md:hidden border-t border-gray-200 py-4">
        <div class="space-y-2">
          <template v-if="!authStore.isAuthenticated">
            <router-link
              to="/"
              class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              Home
            </router-link>
            <router-link
              to="/login"
              class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              Login
            </router-link>
            <router-link
              to="/signup"
              class="block px-3 py-2 text-primary-600 hover:text-primary-700 transition-colors"
            >
              Sign Up
            </router-link>
          </template>

          <template v-else>
            <!-- Mobile role-based navigation -->
            <template v-if="authStore.userRole === 'BUYER'">
              <router-link
                to="/buyer/dashboard"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Browse Gas
              </router-link>
              <router-link
                to="/buyer/orders"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                My Orders
              </router-link>
            </template>

            <template v-if="authStore.userRole === 'SELLER'">
              <router-link
                to="/seller/dashboard"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Dashboard
              </router-link>
              <router-link
                to="/seller/inventory"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Inventory
              </router-link>
              <router-link
                to="/seller/orders"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Orders
              </router-link>
            </template>

            <template v-if="authStore.userRole === 'ADMIN'">
              <router-link
                to="/admin/dashboard"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Dashboard
              </router-link>
              <router-link
                to="/admin/orders"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Orders
              </router-link>
              <router-link
                to="/admin/invoices"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Invoices
              </router-link>
              <router-link
                to="/admin/users"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Users
              </router-link>
              <router-link
                to="/admin/reports"
                class="block px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Reports
              </router-link>
            </template>

            <div class="border-t border-gray-200 pt-2 mt-2">
              <div class="px-3 py-2 text-sm text-gray-700">
                <div class="font-medium">{{ getUserDisplayName() }}</div>
                <div class="text-gray-500">{{ authStore.user?.user?.email }}</div>
              </div>
              <button
                @click="handleLogout"
                class="block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                Sign out
              </button>
            </div>
          </template>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

const router = useRouter();
const authStore = useAuthStore();

const showUserMenu = ref(false);
const showMobileMenu = ref(false);
const userMenuRef = ref<HTMLElement>();

const getUserDisplayName = () => {
  if (!authStore.user) return "User";

  const user = authStore.user.user;
  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }
  if (user.first_name) {
    return user.first_name;
  }
  return user.username || user.email || "User";
};

const userInitials = computed(() => {
  const displayName = getUserDisplayName();
  return displayName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
});

const handleLogout = () => {
  authStore.logout();
  showUserMenu.value = false;
  showMobileMenu.value = false;
  router.push("/");
};

// Close menus when clicking outside
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
