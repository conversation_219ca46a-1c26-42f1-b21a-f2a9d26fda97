import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/SimpleHome.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/signup',
      name: 'signup',
      component: () => import('../views/auth/SignupView.vue'),
      meta: { requiresGuest: true }
    },
    // Buyer Routes
    {
      path: '/buyer',
      name: 'buyer',
      redirect: '/buyer/dashboard',
      meta: { requiresAuth: true, role: 'BUYER' }
    },
    {
      path: '/buyer/dashboard',
      name: 'buyer-dashboard',
      component: () => import('../views/buyer/DashboardView.vue'),
      meta: { requiresAuth: true, role: 'BUYER' }
    },
    {
      path: '/buyer/order',
      name: 'buyer-order',
      component: () => import('../views/buyer/OrderView.vue'),
      meta: { requiresAuth: true, role: 'BUYER' }
    },
    {
      path: '/buyer/orders',
      name: 'buyer-orders',
      component: () => import('../views/buyer/OrdersView.vue'),
      meta: { requiresAuth: true, role: 'BUYER' }
    },
    {
      path: '/buyer/ratings/order/:id',
      name: 'buyer-rating',
      component: () => import('../views/buyer/RatingView.vue'),
      meta: { requiresAuth: true, role: 'BUYER' }
    },
    // Seller Routes
    {
      path: '/seller',
      name: 'seller',
      redirect: '/seller/dashboard',
      meta: { requiresAuth: true, role: 'SELLER' }
    },
    {
      path: '/seller/dashboard',
      name: 'seller-dashboard',
      component: () => import('../views/seller/DashboardView.vue'),
      meta: { requiresAuth: true, role: 'SELLER' }
    },
    {
      path: '/seller/inventory',
      name: 'seller-inventory',
      component: () => import('../views/seller/InventoryView.vue'),
      meta: { requiresAuth: true, role: 'SELLER' }
    },
    {
      path: '/seller/orders',
      name: 'seller-orders',
      component: () => import('../views/seller/OrdersView.vue'),
      meta: { requiresAuth: true, role: 'SELLER' }
    },
    {
      path: '/seller/invoice/generate/:id',
      name: 'seller-invoice',
      component: () => import('../views/seller/InvoiceView.vue'),
      meta: { requiresAuth: true, role: 'SELLER' }
    },
    // Admin Routes
    {
      path: '/admin',
      name: 'admin',
      redirect: '/admin/dashboard',
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: () => import('../views/admin/DashboardView.vue'),
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
    {
      path: '/admin/orders',
      name: 'admin-orders',
      component: () => import('../views/admin/OrdersView.vue'),
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
    {
      path: '/admin/invoices',
      name: 'admin-invoices',
      component: () => import('../views/admin/InvoicesView.vue'),
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
    {
      path: '/admin/users',
      name: 'admin-users',
      component: () => import('../views/admin/UsersView.vue'),
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
    {
      path: '/admin/reports',
      name: 'admin-reports',
      component: () => import('../views/admin/ReportsView.vue'),
      meta: { requiresAuth: true, role: 'ADMIN' }
    },
  ],
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({ name: 'login' })
      return
    }

    // Check role-based access
    if (to.meta.role && authStore.userRole !== to.meta.role) {
      // Redirect to appropriate dashboard based on user role
      const roleRedirects = {
        BUYER: 'buyer-dashboard',
        SELLER: 'seller-dashboard',
        ADMIN: 'admin-dashboard'
      }
      next({ name: roleRedirects[authStore.userRole as keyof typeof roleRedirects] || 'home' })
      return
    }
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect to appropriate dashboard based on user role
    const roleRedirects = {
      BUYER: 'buyer-dashboard',
      SELLER: 'seller-dashboard',
      ADMIN: 'admin-dashboard'
    }
    next({ name: roleRedirects[authStore.userRole as keyof typeof roleRedirects] || 'home' })
    return
  }

  next()
})

export default router
