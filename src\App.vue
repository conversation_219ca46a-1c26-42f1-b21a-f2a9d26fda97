<script setup lang="ts">
import { RouterView } from "vue-router";
</script>

<template>
  <div id="app" style="min-height: 100vh; background: #f9fafb; font-size: 18px;">
    <!-- Simple Header -->
    <header style="background: white; padding: 1rem 2rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
      <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
        <h1 style="font-size: 2rem; font-weight: bold; color: #2563eb; margin: 0;">
          🚀 GasGo Rwanda
        </h1>
        <nav style="display: flex; gap: 2rem;">
          <router-link to="/" style="color: #374151; text-decoration: none; font-size: 1.1rem; font-weight: 500;">
            Home
          </router-link>
          <router-link to="/login" style="color: #374151; text-decoration: none; font-size: 1.1rem; font-weight: 500;">
            Login
          </router-link>
          <router-link to="/signup" style="background: #2563eb; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; font-size: 1.1rem; font-weight: 500;">
            Sign Up
          </router-link>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <main style="flex: 1; padding: 2rem;">
      <RouterView />
    </main>
  </div>
</template>

<style>
#app {
  display: flex;
  flex-direction: column;
}

a:hover {
  opacity: 0.8;
}
</style>
