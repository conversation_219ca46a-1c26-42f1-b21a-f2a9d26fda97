// API service for Gas Stock Management Backend
// This handles all API calls to the actual backend server

import axios from 'axios'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || false

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/v1/token/refresh/`, {
            refresh: refreshToken
          })

          const { access } = response.data
          localStorage.setItem('access_token', access)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`
          return apiClient(originalRequest)
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          localStorage.removeItem('user')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }
    }

    return Promise.reject(error)
  }
)

// Mock data for development
const mockUsers = [
  {
    id: 1,
    user: {
      id: 1,
      username: 'admin_demo',
      email: '<EMAIL>',
      first_name: 'Admin',
      last_name: 'User'
    },
    role: 'ADMIN' as const,
    phone_number: '+250 788 123 456',
    address: 'Kigali, Rwanda'
  },
  {
    id: 2,
    user: {
      id: 2,
      username: 'buyer_demo',
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Buyer'
    },
    role: 'BUYER' as const,
    phone_number: '+250 788 987 654',
    address: 'Gasabo, Kigali'
  },
  {
    id: 3,
    user: {
      id: 3,
      username: 'seller_demo',
      email: '<EMAIL>',
      first_name: 'Jane',
      last_name: 'Seller'
    },
    role: 'SELLER' as const,
    phone_number: '+250 788 456 789',
    address: 'Kicukiro, Kigali'
  }
]

const mockOrders = [
  {
    id: 1,
    buyer: {
      id: 2,
      user: {
        username: 'buyer_demo',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Buyer'
      },
      phone_number: '+250 788 987 654',
      address: 'Gasabo, Kigali'
    },
    gas_inventory: {
      id: 1,
      brand: 'Meru',
      weight_kg: 12,
      unit_price: 15000,
      seller: {
        id: 3,
        user: {
          username: 'seller_demo',
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Seller'
        }
      }
    },
    quantity: 2,
    total_price: 30000,
    status: 'PENDING' as const,
    delivery_address: 'KG 15 Ave, Kigali',
    contact_phone: '+250 788 987 654',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    buyer: {
      id: 2,
      user: {
        username: 'buyer_demo',
        email: '<EMAIL>',
        first_name: 'Alice',
        last_name: 'Smith'
      },
      phone_number: '+250 788 111 222',
      address: 'Nyarugenge, Kigali'
    },
    gas_inventory: {
      id: 2,
      brand: 'K-Gas',
      weight_kg: 6,
      unit_price: 8000,
      seller: {
        id: 3,
        user: {
          username: 'seller_demo',
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Seller'
        }
      }
    },
    quantity: 1,
    total_price: 8000,
    status: 'APPROVED' as const,
    delivery_address: 'KG 25 St, Kigali',
    contact_phone: '+250 788 111 222',
    created_at: '2024-01-14T14:20:00Z',
    updated_at: '2024-01-14T15:20:00Z'
  }
]

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Helper function to check if backend is available
const isBackendAvailable = async (): Promise<boolean> => {
  try {
    await axios.get(`${API_BASE_URL}/health`, { timeout: 2000 })
    return true
  } catch {
    return false
  }
}

// API Service Methods
export const apiService = {
  // Authentication
  async login(username: string, password: string) {
    try {
      // Try real API first
      if (!USE_MOCK_DATA) {
        try {
          const response = await apiClient.post('/v1/login/', {
            username,
            password
          })

          const { access, refresh } = response.data

          // Store tokens
          localStorage.setItem('access_token', access)
          localStorage.setItem('refresh_token', refresh)

          // Get user profile
          const userProfile = await this.getCurrentUserProfile()

          return {
            success: true,
            user: userProfile,
            tokens: { access, refresh }
          }
        } catch (apiError: any) {
          // If API fails, fall back to mock data
          console.warn('API unavailable, using mock data:', apiError.message)
        }
      }

      // Fallback to mock data
      await delay(1000) // Simulate network delay

      const user = mockUsers.find(u => u.user.username === username || u.user.email === username)

      if (user && password === 'demo123') {
        // Store tokens in localStorage for mock mode
        const mockTokens = {
          access: 'mock_access_token_' + Date.now(),
          refresh: 'mock_refresh_token_' + Date.now()
        }

        localStorage.setItem('access_token', mockTokens.access)
        localStorage.setItem('refresh_token', mockTokens.refresh)

        return {
          success: true,
          user: user,
          tokens: mockTokens
        }
      } else {
        return {
          success: false,
          error: 'Invalid credentials. For demo mode, use: admin_demo, buyer_demo, or seller_demo with password: demo123'
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Login failed'
      }
    }
  },

  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
    role: 'BUYER' | 'SELLER' | 'ADMIN'
    phone_number?: string
    address?: string
  }) {
    try {
      // Try real API first
      if (!USE_MOCK_DATA) {
        try {
          const response = await apiClient.post('/register/', userData)

          // Auto-login after successful registration
          const loginResult = await this.login(userData.username, userData.password)

          return {
            success: true,
            user: loginResult.user,
            message: response.data.message
          }
        } catch (apiError: any) {
          console.warn('API unavailable for registration, using mock data:', apiError.message)
        }
      }

      // Fallback to mock registration
      await delay(1000) // Simulate network delay

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.user.email === userData.email || u.user.username === userData.username)

      if (existingUser) {
        return {
          success: false,
          error: 'User with this email or username already exists'
        }
      }

      // Create new mock user
      const newUser = {
        id: mockUsers.length + 1,
        user: {
          id: mockUsers.length + 1,
          username: userData.username,
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name
        },
        role: userData.role,
        phone_number: userData.phone_number,
        address: userData.address
      }

      // Add to mock users (in memory only)
      mockUsers.push(newUser)

      // Auto-login the new user
      const loginResult = await this.login(userData.username, userData.password)

      return {
        success: true,
        user: loginResult.user,
        message: 'Registration successful! (Demo mode - data not persisted)'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Registration failed'
      }
    }
  },

  // User Profile
  async getCurrentUserProfile() {
    try {
      const response = await apiClient.get('/profiles/me/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get user profile')
    }
  },

  async getAllUsers() {
    try {
      const response = await apiClient.get('/profiles/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get users')
    }
  },

  // Gas Inventory
  async getGasInventory(filters?: {
    brand?: string
    weight?: number
    location?: string
    min_price?: number
    max_price?: number
    seller?: number
  }) {
    try {
      const params = new URLSearchParams()
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString())
          }
        })
      }

      const response = await apiClient.get(`/v1/gas/?${params.toString()}`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get gas inventory')
    }
  },

  async getGasInventoryItem(id: number) {
    try {
      const response = await apiClient.get(`/inventory/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get gas inventory item')
    }
  },

  async createGasInventoryItem(data: {
    brand: string
    weight_kg: number
    quantity: number
    unit_price: number
    location: string
  }) {
    try {
      const response = await apiClient.post('/v1/seller/inventory/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create gas inventory item')
    }
  },

  async updateGasInventoryItem(id: number, data: Partial<{
    brand: string
    weight_kg: number
    quantity: number
    unit_price: number
    location: string
  }>) {
    try {
      const response = await apiClient.patch(`/inventory/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update gas inventory item')
    }
  },

  async deleteGasInventoryItem(id: number) {
    try {
      await apiClient.delete(`/inventory/${id}/`)
      return { success: true }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to delete gas inventory item')
    }
  },

  async getMyInventory() {
    try {
      const response = await apiClient.get('/v1/seller/inventory/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get my inventory')
    }
  },

  // Orders
  async getAllOrders() {
    try {
      // Try real API first
      if (!USE_MOCK_DATA) {
        try {
          const response = await apiClient.get('/orders/')
          return response.data
        } catch (apiError: any) {
          console.warn('API unavailable for orders, using mock data:', apiError.message)
        }
      }

      // Fallback to mock data
      await delay(500)
      return mockOrders
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get orders')
    }
  },

  async getOrder(id: number) {
    try {
      const response = await apiClient.get(`/orders/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get order')
    }
  },

  async createOrder(data: {
    gas_inventory: number
    quantity: number
    delivery_address: string
    contact_phone: string
  }) {
    try {
      const response = await apiClient.post('/v1/orders/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create order')
    }
  },

  async updateOrder(id: number, data: Partial<{
    delivery_address: string
    contact_phone: string
  }>) {
    try {
      const response = await apiClient.patch(`/orders/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update order')
    }
  },

  async deleteOrder(id: number) {
    try {
      await apiClient.delete(`/orders/${id}/`)
      return { success: true }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to delete order')
    }
  },

  async getMyOrders() {
    try {
      const response = await apiClient.get('/orders/my_orders/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get my orders')
    }
  },

  async getSellerOrders() {
    try {
      const response = await apiClient.get('/v1/seller/orders/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get seller orders')
    }
  },

  async approveOrder(id: number) {
    try {
      // Try real API first
      if (!USE_MOCK_DATA) {
        try {
          const response = await apiClient.post(`/orders/${id}/approve/`)
          return response.data
        } catch (apiError: any) {
          console.warn('API unavailable for approve order, using mock data:', apiError.message)
        }
      }

      // Fallback to mock data
      await delay(500)
      const order = mockOrders.find(o => o.id === id)
      if (order) {
        order.status = 'APPROVED'
        order.updated_at = new Date().toISOString()
      }
      return { success: true, message: 'Order approved successfully (demo mode)' }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to approve order')
    }
  },

  async rejectOrder(id: number) {
    try {
      // Try real API first
      if (!USE_MOCK_DATA) {
        try {
          const response = await apiClient.post(`/orders/${id}/reject/`)
          return response.data
        } catch (apiError: any) {
          console.warn('API unavailable for reject order, using mock data:', apiError.message)
        }
      }

      // Fallback to mock data
      await delay(500)
      const order = mockOrders.find(o => o.id === id)
      if (order) {
        order.status = 'REJECTED'
        order.updated_at = new Date().toISOString()
      }
      return { success: true, message: 'Order rejected successfully (demo mode)' }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to reject order')
    }
  },

  async cancelOrder(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/cancel/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to cancel order')
    }
  },

  async markOrderAsDelivered(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/mark-delivered/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to mark order as delivered')
    }
  },

  async getPendingOrders() {
    try {
      const response = await apiClient.get('/v1/admin/orders/pending/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get pending orders')
    }
  },

  // Invoices
  async getAllInvoices() {
    try {
      const response = await apiClient.get('/invoices/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get invoices')
    }
  },

  async getInvoice(id: number) {
    try {
      const response = await apiClient.get(`/invoices/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get invoice')
    }
  },

  async createInvoice(data: { order: number }) {
    try {
      const response = await apiClient.post('/v1/seller/invoice/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create invoice')
    }
  },

  async approveInvoice(id: number) {
    try {
      const response = await apiClient.post(`/invoices/${id}/approve/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to approve invoice')
    }
  },

  async markInvoiceAsPaid(id: number) {
    try {
      const response = await apiClient.post(`/invoices/${id}/mark-as-paid/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to mark invoice as paid')
    }
  },

  async getPendingInvoices() {
    try {
      const response = await apiClient.get('/v1/admin/invoices/pending/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get pending invoices')
    }
  },

  // Payments
  async getAllPayments() {
    try {
      const response = await apiClient.get('/payments/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get payments')
    }
  },

  async getPayment(id: number) {
    try {
      const response = await apiClient.get(`/payments/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get payment')
    }
  },

  async createPayment(data: {
    invoice: number
    amount: number
    payment_method: string
    transaction_id: string
  }) {
    try {
      const response = await apiClient.post('/payments/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create payment')
    }
  },

  async updatePayment(id: number, data: Partial<{
    status: string
    transaction_id: string
  }>) {
    try {
      const response = await apiClient.patch(`/payments/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update payment')
    }
  },

  // Ratings
  async getAllRatings() {
    try {
      const response = await apiClient.get('/ratings/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get ratings')
    }
  },

  async getRating(id: number) {
    try {
      const response = await apiClient.get(`/ratings/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get rating')
    }
  },

  async createRating(data: {
    order: number
    rating: number
    comment: string
  }) {
    try {
      const response = await apiClient.post('/v1/feedback/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create rating')
    }
  }
}

export default apiService
