// API service for Gas Stock Management Backend
// This handles all API calls to the actual backend server

import axios from 'axios'

// API Configuration
const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:8000'

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/v1/token/refresh/`, {
            refresh: refreshToken
          })

          const { access } = response.data
          localStorage.setItem('access_token', access)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`
          return apiClient(originalRequest)
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          localStorage.removeItem('user')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }
    }

    return Promise.reject(error)
  }
)

// API Service Methods
export const apiService = {
  // Authentication
  async login(username: string, password: string) {
    try {
      const response = await apiClient.post('/v1/login/', {
        username,
        password
      })

      const { access, refresh } = response.data

      // Store tokens
      localStorage.setItem('access_token', access)
      localStorage.setItem('refresh_token', refresh)

      // Get user profile
      const userProfile = await this.getCurrentUserProfile()

      return {
        success: true,
        user: userProfile,
        tokens: { access, refresh }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Login failed'
      }
    }
  },

  async register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
    role: 'BUYER' | 'SELLER' | 'ADMIN'
    phone_number?: string
    address?: string
  }) {
    try {
      const response = await apiClient.post('/register/', userData)

      // Auto-login after successful registration
      const loginResult = await this.login(userData.username, userData.password)

      return {
        success: true,
        user: loginResult.user,
        message: response.data.message
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Registration failed'
      }
    }
  },

  // User Profile
  async getCurrentUserProfile() {
    try {
      const response = await apiClient.get('/profiles/me/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get user profile')
    }
  },

  async getAllUsers() {
    try {
      const response = await apiClient.get('/profiles/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get users')
    }
  },

  // Gas Inventory
  async getGasInventory(filters?: {
    brand?: string
    weight?: number
    location?: string
    min_price?: number
    max_price?: number
    seller?: number
  }) {
    try {
      const params = new URLSearchParams()
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString())
          }
        })
      }

      const response = await apiClient.get(`/v1/gas/?${params.toString()}`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get gas inventory')
    }
  },

  async getGasInventoryItem(id: number) {
    try {
      const response = await apiClient.get(`/inventory/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get gas inventory item')
    }
  },

  async createGasInventoryItem(data: {
    brand: string
    weight_kg: number
    quantity: number
    unit_price: number
    location: string
  }) {
    try {
      const response = await apiClient.post('/v1/seller/inventory/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create gas inventory item')
    }
  },

  async updateGasInventoryItem(id: number, data: Partial<{
    brand: string
    weight_kg: number
    quantity: number
    unit_price: number
    location: string
  }>) {
    try {
      const response = await apiClient.patch(`/inventory/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update gas inventory item')
    }
  },

  async deleteGasInventoryItem(id: number) {
    try {
      await apiClient.delete(`/inventory/${id}/`)
      return { success: true }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to delete gas inventory item')
    }
  },

  async getMyInventory() {
    try {
      const response = await apiClient.get('/v1/seller/inventory/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get my inventory')
    }
  },

  // Orders
  async getAllOrders() {
    try {
      const response = await apiClient.get('/orders/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get orders')
    }
  },

  async getOrder(id: number) {
    try {
      const response = await apiClient.get(`/orders/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get order')
    }
  },

  async createOrder(data: {
    gas_inventory: number
    quantity: number
    delivery_address: string
    contact_phone: string
  }) {
    try {
      const response = await apiClient.post('/v1/orders/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create order')
    }
  },

  async updateOrder(id: number, data: Partial<{
    delivery_address: string
    contact_phone: string
  }>) {
    try {
      const response = await apiClient.patch(`/orders/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update order')
    }
  },

  async deleteOrder(id: number) {
    try {
      await apiClient.delete(`/orders/${id}/`)
      return { success: true }
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to delete order')
    }
  },

  async getMyOrders() {
    try {
      const response = await apiClient.get('/orders/my_orders/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get my orders')
    }
  },

  async getSellerOrders() {
    try {
      const response = await apiClient.get('/v1/seller/orders/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get seller orders')
    }
  },

  async approveOrder(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/approve/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to approve order')
    }
  },

  async rejectOrder(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/reject/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to reject order')
    }
  },

  async cancelOrder(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/cancel/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to cancel order')
    }
  },

  async markOrderAsDelivered(id: number) {
    try {
      const response = await apiClient.post(`/orders/${id}/mark-delivered/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to mark order as delivered')
    }
  },

  async getPendingOrders() {
    try {
      const response = await apiClient.get('/v1/admin/orders/pending/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get pending orders')
    }
  },

  // Invoices
  async getAllInvoices() {
    try {
      const response = await apiClient.get('/invoices/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get invoices')
    }
  },

  async getInvoice(id: number) {
    try {
      const response = await apiClient.get(`/invoices/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get invoice')
    }
  },

  async createInvoice(data: { order: number }) {
    try {
      const response = await apiClient.post('/v1/seller/invoice/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create invoice')
    }
  },

  async approveInvoice(id: number) {
    try {
      const response = await apiClient.post(`/invoices/${id}/approve/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to approve invoice')
    }
  },

  async markInvoiceAsPaid(id: number) {
    try {
      const response = await apiClient.post(`/invoices/${id}/mark-as-paid/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to mark invoice as paid')
    }
  },

  async getPendingInvoices() {
    try {
      const response = await apiClient.get('/v1/admin/invoices/pending/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get pending invoices')
    }
  },

  // Payments
  async getAllPayments() {
    try {
      const response = await apiClient.get('/payments/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get payments')
    }
  },

  async getPayment(id: number) {
    try {
      const response = await apiClient.get(`/payments/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get payment')
    }
  },

  async createPayment(data: {
    invoice: number
    amount: number
    payment_method: string
    transaction_id: string
  }) {
    try {
      const response = await apiClient.post('/payments/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create payment')
    }
  },

  async updatePayment(id: number, data: Partial<{
    status: string
    transaction_id: string
  }>) {
    try {
      const response = await apiClient.patch(`/payments/${id}/`, data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to update payment')
    }
  },

  // Ratings
  async getAllRatings() {
    try {
      const response = await apiClient.get('/ratings/')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get ratings')
    }
  },

  async getRating(id: number) {
    try {
      const response = await apiClient.get(`/ratings/${id}/`)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get rating')
    }
  },

  async createRating(data: {
    order: number
    rating: number
    comment: string
  }) {
    try {
      const response = await apiClient.post('/v1/feedback/', data)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to create rating')
    }
  }
}

export default apiService
