import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import apiService from '@/services/apiService'

export interface User {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
  }
  role: 'BUYER' | 'SELLER' | 'ADMIN'
  phone_number?: string
  address?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('access_token'))

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || null)

  // Load user from localStorage on store initialization
  const storedUser = localStorage.getItem('user')
  if (storedUser) {
    try {
      user.value = JSON.parse(storedUser)
    } catch (error) {
      console.error('Error parsing stored user:', error)
      localStorage.removeItem('user')
    }
  }

  async function login(username: string, password: string) {
    try {
      const result = await apiService.login(username, password)

      if (result.success) {
        user.value = result.user
        token.value = result.tokens.access

        localStorage.setItem('user', JSON.stringify(result.user))

        return { success: true, user: result.user }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return {
        success: false,
        error: error.message || 'Login failed'
      }
    }
  }

  async function register(userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
    role: 'BUYER' | 'SELLER' | 'ADMIN'
    phone_number?: string
    address?: string
  }) {
    try {
      const result = await apiService.register(userData)

      if (result.success) {
        user.value = result.user
        token.value = localStorage.getItem('access_token')

        return { success: true, user: result.user }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: error.message || 'Registration failed'
      }
    }
  }

  function logout() {
    user.value = null
    token.value = null

    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  }

  return {
    user,
    token,
    isAuthenticated,
    userRole,
    login,
    register,
    logout
  }
})
