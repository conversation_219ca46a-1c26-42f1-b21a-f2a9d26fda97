// Mock API service for development
// This simulates backend API calls without requiring a real server

import type { User } from '@/stores/auth'

// Mock users database
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    role: 'buyer',
    name: '<PERSON>er'
  },
  {
    id: '2',
    email: '<EMAIL>',
    role: 'seller',
    name: '<PERSON>',
    businessName: 'Kigali Gas Supplies',
    businessLocation: 'Gasabo',
    businessPhone: '+250 788 123 456'
  },
  {
    id: '3',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Admin User'
  }
]

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const mockApi = {
  async login(email: string, password: string) {
    await delay(1000) // Simulate network delay

    // Simple password check (in real app, this would be handled securely on backend)
    if (password !== 'demo123') {
      throw new Error('Invalid credentials')
    }

    const user = mockUsers.find(u => u.email === email)
    if (!user) {
      throw new Error('User not found')
    }

    // Generate a mock JWT token
    const token = `mock-jwt-token-${user.id}-${Date.now()}`

    return {
      user,
      token
    }
  },

  async register(userData: {
    email: string
    password: string
    role: 'buyer' | 'seller' | 'admin'
    name?: string
    businessName?: string
    businessLocation?: string
    businessPhone?: string
  }) {
    await delay(1000) // Simulate network delay

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email)
    if (existingUser) {
      throw new Error('User already exists')
    }

    // Create new user
    const newUser: User = {
      id: `${mockUsers.length + 1}`,
      email: userData.email,
      role: userData.role,
      ...(userData.name && { name: userData.name }),
      ...(userData.businessName && { businessName: userData.businessName }),
      ...(userData.businessLocation && { businessLocation: userData.businessLocation }),
      ...(userData.businessPhone && { businessPhone: userData.businessPhone })
    }

    // Add to mock database
    mockUsers.push(newUser)

    // Generate a mock JWT token
    const token = `mock-jwt-token-${newUser.id}-${Date.now()}`

    return {
      user: newUser,
      token
    }
  }
}

// Override axios for mock API
import axios from 'axios'

// Intercept API calls and redirect to mock functions
axios.interceptors.request.use(async (config) => {
  const url = config.url || ''

  if (url.includes('/api/auth/login')) {
    const { email, password } = config.data
    try {
      const result = await mockApi.login(email, password)
      // Create a mock response
      const mockResponse = {
        data: result,
        status: 200,
        statusText: 'OK',
        headers: {},
        config
      }
      // Return the mock response directly
      return Promise.reject({
        response: mockResponse,
        isAxiosError: true,
        config,
        request: {},
        message: 'Mock response'
      })
    } catch (error) {
      return Promise.reject({
        response: {
          data: { message: (error as Error).message },
          status: 401,
          statusText: 'Unauthorized',
          headers: {},
          config
        },
        isAxiosError: true,
        config,
        request: {},
        message: (error as Error).message
      })
    }
  }

  if (url.includes('/api/auth/register')) {
    try {
      const result = await mockApi.register(config.data)
      const mockResponse = {
        data: result,
        status: 201,
        statusText: 'Created',
        headers: {},
        config
      }
      return Promise.reject({
        response: mockResponse,
        isAxiosError: true,
        config,
        request: {},
        message: 'Mock response'
      })
    } catch (error) {
      return Promise.reject({
        response: {
          data: { message: (error as Error).message },
          status: 400,
          statusText: 'Bad Request',
          headers: {},
          config
        },
        isAxiosError: true,
        config,
        request: {},
        message: (error as Error).message
      })
    }
  }

  return config
})

// Handle mock responses
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    // If it's our mock response, return it as successful
    if (error.message === 'Mock response' && error.response) {
      return Promise.resolve(error.response)
    }
    return Promise.reject(error)
  }
)
