<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <div class="text-sm text-gray-600">Last updated: {{ new Date().toLocaleString() }}</div>
      </div>

      <!-- Summary Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg
                class="h-8 w-8 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Total Users</h3>
              <p class="text-3xl font-bold text-primary-600 mt-1">{{ dashboardData.totalUsers }}</p>
              <p class="text-sm text-gray-600">
                {{ dashboardData.newUsersThisMonth }} new this month
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg
                class="h-8 w-8 text-secondary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Active Sellers</h3>
              <p class="text-3xl font-bold text-secondary-600 mt-1">
                {{ dashboardData.activeSellers }}
              </p>
              <p class="text-sm text-gray-600">{{ dashboardData.totalSellers }} total registered</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg
                class="h-8 w-8 text-accent-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Pending Approvals</h3>
              <p class="text-3xl font-bold text-accent-600 mt-1">
                {{ dashboardData.pendingOrders }}
              </p>
              <p class="text-sm text-gray-600">
                {{ dashboardData.pendingInvoices }} invoices pending
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg
                class="h-8 w-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">Total Revenue</h3>
              <p class="text-3xl font-bold text-green-600 mt-1">
                {{ formatCurrency(dashboardData.totalRevenue) }}
              </p>
              <p class="text-sm text-gray-600">
                {{ formatCurrency(dashboardData.monthlyRevenue) }} this month
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions and Recent Activity -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <router-link
              to="/admin/orders"
              class="flex items-center justify-center px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                ></path>
              </svg>
              Manage Orders
            </router-link>
            <router-link
              to="/admin/invoices"
              class="flex items-center justify-center px-4 py-3 bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              Review Invoices
            </router-link>
            <router-link
              to="/admin/users"
              class="flex items-center justify-center px-4 py-3 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                ></path>
              </svg>
              Manage Users
            </router-link>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div class="space-y-4">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="flex items-start space-x-3"
            >
              <div class="flex-shrink-0">
                <div
                  :class="[
                    'w-2 h-2 rounded-full mt-2',
                    activity.type === 'order'
                      ? 'bg-blue-500'
                      : activity.type === 'user'
                      ? 'bg-green-500'
                      : activity.type === 'invoice'
                      ? 'bg-yellow-500'
                      : 'bg-gray-500',
                  ]"
                ></div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ activity.message }}</p>
                <p class="text-xs text-gray-500">{{ activity.timestamp }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Platform Status</span>
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                Online
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Payment Gateway</span>
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                Connected
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">SMS Service</span>
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                Active
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Orders Summary -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Orders Overview</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <p class="text-2xl font-bold text-yellow-600">{{ dashboardData.orderStats.pending }}</p>
            <p class="text-sm text-gray-600">Pending Approval</p>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <p class="text-2xl font-bold text-blue-600">{{ dashboardData.orderStats.approved }}</p>
            <p class="text-sm text-gray-600">Approved</p>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <p class="text-2xl font-bold text-green-600">
              {{ dashboardData.orderStats.delivered }}
            </p>
            <p class="text-sm text-gray-600">Delivered</p>
          </div>
          <div class="text-center p-4 bg-red-50 rounded-lg">
            <p class="text-2xl font-bold text-red-600">{{ dashboardData.orderStats.rejected }}</p>
            <p class="text-sm text-gray-600">Rejected</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

interface DashboardData {
  totalUsers: number;
  newUsersThisMonth: number;
  activeSellers: number;
  totalSellers: number;
  pendingOrders: number;
  pendingInvoices: number;
  totalRevenue: number;
  monthlyRevenue: number;
  orderStats: {
    pending: number;
    approved: number;
    delivered: number;
    rejected: number;
  };
}

interface Activity {
  id: string;
  type: "order" | "user" | "invoice" | "system";
  message: string;
  timestamp: string;
}

const dashboardData = ref<DashboardData>({
  totalUsers: 1247,
  newUsersThisMonth: 89,
  activeSellers: 156,
  totalSellers: 203,
  pendingOrders: 23,
  pendingInvoices: 12,
  totalRevenue: 2450000,
  monthlyRevenue: 340000,
  orderStats: {
    pending: 23,
    approved: 145,
    delivered: 892,
    rejected: 8,
  },
});

const recentActivities = ref<Activity[]>([
  {
    id: "1",
    type: "user",
    message: 'New seller "Kigali Gas Pro" registered',
    timestamp: "2 hours ago",
  },
  {
    id: "2",
    type: "order",
    message: "Order #1247 approved for delivery",
    timestamp: "3 hours ago",
  },
  {
    id: "3",
    type: "invoice",
    message: "Invoice #INV-2024-0156 processed",
    timestamp: "4 hours ago",
  },
  {
    id: "4",
    type: "order",
    message: "Order #1246 completed successfully",
    timestamp: "6 hours ago",
  },
  {
    id: "5",
    type: "user",
    message: "Buyer account verified: <EMAIL>",
    timestamp: "8 hours ago",
  },
]);

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("rw-RW", {
    style: "currency",
    currency: "RWF",
    minimumFractionDigits: 0,
  }).format(amount);
};

onMounted(() => {
  // In a real app, this would fetch data from an API
  console.log("Admin dashboard loaded");
});
</script>
