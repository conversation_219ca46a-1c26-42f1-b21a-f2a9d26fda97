<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="flex justify-center">
          <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">G</span>
          </div>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Create your account</h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <router-link to="/login" class="font-medium text-primary-600 hover:text-primary-500">
            sign in to your existing account
          </router-link>
        </p>

        <!-- Demo Mode Notice -->
        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p class="text-sm text-blue-800 text-center">
            <strong>Demo Mode:</strong> If the backend is unavailable, you can still test the app!
            Use any username/email with password: <code class="bg-blue-100 px-1 rounded">demo123</code>
          </p>
        </div>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleSignup">
        <!-- Role Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3"> I am a: </label>
          <div class="grid grid-cols-3 gap-3">
            <button
              type="button"
              @click="form.role = 'BUYER'"
              :class="[
                'flex items-center justify-center px-4 py-3 border rounded-lg text-sm font-medium transition-colors duration-200',
                form.role === 'BUYER'
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
              ]"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                ></path>
              </svg>
              Buyer
            </button>
            <button
              type="button"
              @click="form.role = 'SELLER'"
              :class="[
                'flex items-center justify-center px-4 py-3 border rounded-lg text-sm font-medium transition-colors duration-200',
                form.role === 'SELLER'
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
              ]"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                ></path>
              </svg>
              Seller
            </button>
            <button
              type="button"
              @click="form.role = 'ADMIN'"
              :class="[
                'flex items-center justify-center px-4 py-3 border rounded-lg text-sm font-medium transition-colors duration-200',
                form.role === 'ADMIN'
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
              ]"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                ></path>
              </svg>
              Admin
            </button>
          </div>
        </div>

        <div class="space-y-4">
          <!-- Username -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              Username <span class="text-red-500">*</span>
            </label>
            <input
              id="username"
              v-model="form.username"
              name="username"
              type="text"
              autocomplete="username"
              required
              class="input-field mt-1"
              placeholder="Choose a username"
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email address <span class="text-red-500">*</span>
            </label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="input-field mt-1"
              placeholder="Enter your email"
            />
          </div>

          <!-- First Name -->
          <div>
            <label for="firstName" class="block text-sm font-medium text-gray-700">
              First Name <span class="text-red-500">*</span>
            </label>
            <input
              id="firstName"
              v-model="form.first_name"
              name="firstName"
              type="text"
              required
              class="input-field mt-1"
              placeholder="Enter your first name"
            />
          </div>

          <!-- Last Name -->
          <div>
            <label for="lastName" class="block text-sm font-medium text-gray-700">
              Last Name <span class="text-red-500">*</span>
            </label>
            <input
              id="lastName"
              v-model="form.last_name"
              name="lastName"
              type="text"
              required
              class="input-field mt-1"
              placeholder="Enter your last name"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700"> Password </label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="new-password"
              required
              class="input-field mt-1"
              placeholder="Create a password"
            />
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              name="confirmPassword"
              type="password"
              autocomplete="new-password"
              required
              class="input-field mt-1"
              placeholder="Confirm your password"
            />
          </div>

          <!-- Phone Number -->
          <div>
            <label for="phoneNumber" class="block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <input
              id="phoneNumber"
              v-model="form.phone_number"
              name="phoneNumber"
              type="tel"
              class="input-field mt-1"
              placeholder="Enter your phone number"
            />
          </div>

          <!-- Address -->
          <div>
            <label for="address" class="block text-sm font-medium text-gray-700">
              Address
            </label>
            <textarea
              id="address"
              v-model="form.address"
              name="address"
              rows="3"
              class="input-field mt-1"
              placeholder="Enter your address"
            ></textarea>
          </div>

          <!-- Seller specific fields -->
          <template v-if="form.role === 'SELLER'">
            <div>
              <label for="businessName" class="block text-sm font-medium text-gray-700">
                Business Name <span class="text-red-500">*</span>
              </label>
              <input
                id="businessName"
                v-model="form.businessName"
                name="businessName"
                type="text"
                required
                class="input-field mt-1"
                placeholder="Enter your business name"
              />
            </div>

            <div>
              <label for="businessContact" class="block text-sm font-medium text-gray-700">
                Contact Person Name <span class="text-red-500">*</span>
              </label>
              <input
                id="businessContact"
                v-model="form.name"
                name="businessContact"
                type="text"
                required
                class="input-field mt-1"
                placeholder="Enter contact person name"
              />
            </div>

            <div>
              <label for="businessPhone" class="block text-sm font-medium text-gray-700">
                Business Phone <span class="text-red-500">*</span>
              </label>
              <input
                id="businessPhone"
                v-model="form.businessPhone"
                name="businessPhone"
                type="tel"
                required
                class="input-field mt-1"
                placeholder="Enter business phone number"
              />
            </div>

            <div>
              <label for="businessLocation" class="block text-sm font-medium text-gray-700">
                Business Location <span class="text-red-500">*</span>
              </label>
              <select
                id="businessLocation"
                v-model="form.businessLocation"
                name="businessLocation"
                required
                class="input-field mt-1"
              >
                <option value="">Select district</option>
                <option value="Gasabo">Gasabo</option>
                <option value="Kicukiro">Kicukiro</option>
                <option value="Nyarugenge">Nyarugenge</option>
              </select>
            </div>
          </template>

          <!-- Admin specific fields -->
          <div v-if="form.role === 'ADMIN'">
            <p class="text-sm text-gray-600">
              Admin accounts require approval. Please ensure all information is accurate.
            </p>
          </div>
        </div>

        <!-- Terms and conditions -->
        <div class="flex items-center">
          <input
            id="terms"
            v-model="form.acceptTerms"
            name="terms"
            type="checkbox"
            required
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="terms" class="ml-2 block text-sm text-gray-900">
            I agree to the
            <a href="#" class="text-primary-600 hover:text-primary-500">Terms and Conditions</a>
            and
            <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
          </label>
        </div>

        <!-- Error message -->
        <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg
                class="animate-spin h-5 w-5 text-primary-300"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </span>
            {{ loading ? "Creating account..." : "Create account" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useAuthStore } from "@/stores/auth";

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const loading = ref(false);
const error = ref("");

const form = reactive({
  username: "",
  email: "",
  password: "",
  confirmPassword: "",
  first_name: "",
  last_name: "",
  role: "BUYER" as "BUYER" | "SELLER" | "ADMIN",
  phone_number: "",
  address: "",
  // Seller-specific fields (for UI compatibility)
  businessName: "",
  businessPhone: "",
  businessLocation: "",
  name: "", // For contact person
  acceptTerms: false,
});

// Set role from query parameter if provided
onMounted(() => {
  if (route.query.role) {
    const roleMap: { [key: string]: "BUYER" | "SELLER" | "ADMIN" } = {
      buyer: "BUYER",
      seller: "SELLER",
      admin: "ADMIN"
    };

    if (roleMap[route.query.role as string]) {
      form.role = roleMap[route.query.role as string];
    }
  }
});

const validateForm = () => {
  if (!form.username || !form.email || !form.password || !form.confirmPassword || !form.first_name || !form.last_name) {
    return "Please fill in all required fields";
  }

  if (form.password !== form.confirmPassword) {
    return "Passwords do not match";
  }

  if (form.password.length < 6) {
    return "Password must be at least 6 characters long";
  }

  if (form.role === "SELLER") {
    if (!form.businessName || !form.businessPhone || !form.businessLocation || !form.name) {
      return "Please fill in all business information";
    }
  }

  if (!form.acceptTerms) {
    return "Please accept the terms and conditions";
  }

  return null;
};

const handleSignup = async () => {
  const validationError = validateForm();
  if (validationError) {
    error.value = validationError;
    return;
  }

  loading.value = true;
  error.value = "";

  try {
    const userData = {
      username: form.username,
      email: form.email,
      password: form.password,
      first_name: form.first_name,
      last_name: form.last_name,
      role: form.role,
      ...(form.phone_number && { phone_number: form.phone_number }),
      ...(form.address && { address: form.address }),
    };

    const result = await authStore.register(userData);

    if (result.success) {
      // Redirect based on user role
      const roleRedirects = {
        BUYER: "buyer-dashboard",
        SELLER: "seller-dashboard",
        ADMIN: "admin-dashboard",
      };

      const redirectRoute = roleRedirects[result.user.role as keyof typeof roleRedirects];
      router.push({ name: redirectRoute });
    } else {
      error.value = result.error || "Registration failed";
    }
  } catch (err) {
    error.value = "An unexpected error occurred";
    console.error("Registration error:", err);
  } finally {
    loading.value = false;
  }
};
</script>
