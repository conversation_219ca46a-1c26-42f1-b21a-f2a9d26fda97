<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Manage Orders</h1>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-600">Total Orders: {{ filteredOrders.length }}</div>
          <button
            @click="refreshOrders"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              ></path>
            </svg>
            Refresh
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select v-model="filters.status" class="input-field">
              <option value="">All Statuses</option>
              <option value="pending">Pending Approval</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="delivered">Delivered</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Seller</label>
            <select v-model="filters.seller" class="input-field">
              <option value="">All Sellers</option>
              <option v-for="seller in uniqueSellers" :key="seller" :value="seller">
                {{ seller }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <select v-model="filters.dateRange" class="input-field">
              <option value="">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              v-model="filters.search"
              type="text"
              placeholder="Order ID, Buyer name..."
              class="input-field"
            />
          </div>
        </div>
      </div>

      <!-- Orders Table -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Orders Management</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Order ID
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Date
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Buyer
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Seller
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Product
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Amount
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in filteredOrders" :key="order.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ order.id }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(order.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.buyerName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.sellerName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.productName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatCurrency(order.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusBadgeClass(order.status)">
                    {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex items-center space-x-2">
                    <button
                      @click="viewOrderDetails(order)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      v-if="order.status === 'pending'"
                      @click="approveOrder(order)"
                      class="text-green-600 hover:text-green-900"
                    >
                      Approve
                    </button>
                    <button
                      v-if="order.status === 'pending'"
                      @click="rejectOrder(order)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Reject
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredOrders.length === 0">
                <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                  No orders found matching your criteria.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Order Details Modal -->
    <div
      v-if="selectedOrder"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="closeModal"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold text-gray-900">Order Details - {{ selectedOrder.id }}</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold text-gray-900">Order Information</h4>
              <p><span class="font-medium">Order ID:</span> {{ selectedOrder.id }}</p>
              <p>
                <span class="font-medium">Date:</span> {{ formatDate(selectedOrder.createdAt) }}
              </p>
              <p>
                <span class="font-medium">Status:</span>
                <span :class="getStatusBadgeClass(selectedOrder.status)">
                  {{ selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1) }}
                </span>
              </p>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Product Details</h4>
              <p><span class="font-medium">Product:</span> {{ selectedOrder.productName }}</p>
              <p><span class="font-medium">Quantity:</span> {{ selectedOrder.quantity }}</p>
              <p>
                <span class="font-medium">Amount:</span> {{ formatCurrency(selectedOrder.amount) }}
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold text-gray-900">Buyer Information</h4>
              <p><span class="font-medium">Name:</span> {{ selectedOrder.buyerName }}</p>
              <p><span class="font-medium">Email:</span> {{ selectedOrder.buyerEmail }}</p>
              <p><span class="font-medium">Phone:</span> {{ selectedOrder.buyerPhone }}</p>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">Seller Information</h4>
              <p><span class="font-medium">Business:</span> {{ selectedOrder.sellerName }}</p>
              <p><span class="font-medium">Contact:</span> {{ selectedOrder.sellerContact }}</p>
              <p><span class="font-medium">Location:</span> {{ selectedOrder.sellerLocation }}</p>
            </div>
          </div>

          <div>
            <h4 class="font-semibold text-gray-900">Delivery Information</h4>
            <p><span class="font-medium">Address:</span> {{ selectedOrder.deliveryAddress }}</p>
            <p>
              <span class="font-medium">Instructions:</span>
              {{ selectedOrder.deliveryInstructions || "None" }}
            </p>
          </div>

          <div
            v-if="selectedOrder.status === 'pending'"
            class="flex justify-end space-x-3 pt-4 border-t"
          >
            <button
              @click="rejectOrder(selectedOrder)"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Reject Order
            </button>
            <button
              @click="approveOrder(selectedOrder)"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Approve Order
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Rejection Reason Modal -->
    <div
      v-if="showRejectModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="closeRejectModal"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold text-gray-900">Reject Order</h3>
          <button @click="closeRejectModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <p class="text-gray-600">Please provide a reason for rejecting this order:</p>
          <textarea
            v-model="rejectionReason"
            rows="4"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Enter rejection reason..."
          ></textarea>

          <div class="flex justify-end space-x-3">
            <button
              @click="closeRejectModal"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              @click="confirmRejectOrder"
              :disabled="!rejectionReason.trim()"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Reject Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

interface Order {
  id: string;
  createdAt: string;
  buyerName: string;
  buyerEmail: string;
  buyerPhone: string;
  sellerName: string;
  sellerContact: string;
  sellerLocation: string;
  productName: string;
  quantity: number;
  amount: number;
  status: "pending" | "approved" | "rejected" | "delivered";
  deliveryAddress: string;
  deliveryInstructions?: string;
  rejectionReason?: string;
}

const orders = ref<Order[]>([
  {
    id: "#ORD-2024-001",
    createdAt: "2024-01-15T10:30:00Z",
    buyerName: "John Doe",
    buyerEmail: "<EMAIL>",
    buyerPhone: "+250 788 123 456",
    sellerName: "Kigali Gas Supplies",
    sellerContact: "Jane Seller",
    sellerLocation: "Gasabo",
    productName: "Meru 12KG",
    quantity: 2,
    amount: 30000,
    status: "pending",
    deliveryAddress: "KG 15 Ave, Kigali",
    deliveryInstructions: "Call before delivery",
  },
  {
    id: "#ORD-2024-002",
    createdAt: "2024-01-14T14:20:00Z",
    buyerName: "Alice Smith",
    buyerEmail: "<EMAIL>",
    buyerPhone: "+250 788 987 654",
    sellerName: "Rwanda Gas Pro",
    sellerContact: "Bob Seller",
    sellerLocation: "Kicukiro",
    productName: "K-Gas 6KG",
    quantity: 1,
    amount: 8000,
    status: "approved",
    deliveryAddress: "KG 25 St, Kigali",
  },
  {
    id: "#ORD-2024-003",
    createdAt: "2024-01-13T09:15:00Z",
    buyerName: "David Wilson",
    buyerEmail: "<EMAIL>",
    buyerPhone: "+250 788 456 789",
    sellerName: "Kigali Gas Supplies",
    sellerContact: "Jane Seller",
    sellerLocation: "Gasabo",
    productName: "Meru 25KG",
    quantity: 1,
    amount: 45000,
    status: "delivered",
    deliveryAddress: "KG 30 Ave, Kigali",
  },
  {
    id: "#ORD-2024-004",
    createdAt: "2024-01-12T16:45:00Z",
    buyerName: "Sarah Johnson",
    buyerEmail: "<EMAIL>",
    buyerPhone: "+250 788 321 654",
    sellerName: "Gas Express Rwanda",
    sellerContact: "Mike Seller",
    sellerLocation: "Nyarugenge",
    productName: "K-Gas 12KG",
    quantity: 3,
    amount: 42000,
    status: "rejected",
    deliveryAddress: "KG 40 St, Kigali",
    rejectionReason: "Seller out of stock",
  },
]);

const filters = ref({
  status: "",
  seller: "",
  dateRange: "",
  search: "",
});

const selectedOrder = ref<Order | null>(null);
const showRejectModal = ref(false);
const rejectionReason = ref("");
const orderToReject = ref<Order | null>(null);

const uniqueSellers = computed(() => {
  const sellers = orders.value.map((order) => order.sellerName);
  return [...new Set(sellers)];
});

const filteredOrders = computed(() => {
  let filtered = orders.value;

  // Filter by status
  if (filters.value.status) {
    filtered = filtered.filter((order) => order.status === filters.value.status);
  }

  // Filter by seller
  if (filters.value.seller) {
    filtered = filtered.filter((order) => order.sellerName === filters.value.seller);
  }

  // Filter by date range
  if (filters.value.dateRange) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    filtered = filtered.filter((order) => {
      const orderDate = new Date(order.createdAt);

      switch (filters.value.dateRange) {
        case "today":
          return orderDate >= today;
        case "week":
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          return orderDate >= weekAgo;
        case "month":
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
          return orderDate >= monthAgo;
        default:
          return true;
      }
    });
  }

  // Filter by search
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase();
    filtered = filtered.filter(
      (order) =>
        order.id.toLowerCase().includes(searchTerm) ||
        order.buyerName.toLowerCase().includes(searchTerm) ||
        order.sellerName.toLowerCase().includes(searchTerm) ||
        order.productName.toLowerCase().includes(searchTerm)
    );
  }

  return filtered;
});

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("rw-RW", {
    style: "currency",
    currency: "RWF",
    minimumFractionDigits: 0,
  }).format(amount);
};

const getStatusBadgeClass = (status: string): string => {
  const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";

  switch (status) {
    case "pending":
      return `${baseClasses} bg-yellow-100 text-yellow-800`;
    case "approved":
      return `${baseClasses} bg-blue-100 text-blue-800`;
    case "delivered":
      return `${baseClasses} bg-green-100 text-green-800`;
    case "rejected":
      return `${baseClasses} bg-red-100 text-red-800`;
    default:
      return `${baseClasses} bg-gray-100 text-gray-800`;
  }
};

const viewOrderDetails = (order: Order) => {
  selectedOrder.value = order;
};

const closeModal = () => {
  selectedOrder.value = null;
};

const approveOrder = async (order: Order) => {
  try {
    // In a real app, this would make an API call
    const orderIndex = orders.value.findIndex((o) => o.id === order.id);
    if (orderIndex !== -1) {
      orders.value[orderIndex].status = "approved";
    }

    // Close modal if open
    if (selectedOrder.value?.id === order.id) {
      selectedOrder.value = null;
    }

    // Show success message (you could use a toast notification here)
    alert(`Order ${order.id} has been approved successfully!`);
  } catch (error) {
    console.error("Error approving order:", error);
    alert("Failed to approve order. Please try again.");
  }
};

const rejectOrder = (order: Order) => {
  orderToReject.value = order;
  showRejectModal.value = true;
  rejectionReason.value = "";
};

const closeRejectModal = () => {
  showRejectModal.value = false;
  orderToReject.value = null;
  rejectionReason.value = "";
};

const confirmRejectOrder = async () => {
  if (!orderToReject.value || !rejectionReason.value.trim()) return;

  try {
    // In a real app, this would make an API call
    const orderIndex = orders.value.findIndex((o) => o.id === orderToReject.value!.id);
    if (orderIndex !== -1) {
      orders.value[orderIndex].status = "rejected";
      orders.value[orderIndex].rejectionReason = rejectionReason.value.trim();
    }

    // Close modals
    closeRejectModal();
    if (selectedOrder.value?.id === orderToReject.value.id) {
      selectedOrder.value = null;
    }

    // Show success message
    alert(`Order ${orderToReject.value.id} has been rejected.`);
  } catch (error) {
    console.error("Error rejecting order:", error);
    alert("Failed to reject order. Please try again.");
  }
};

const refreshOrders = () => {
  // In a real app, this would fetch fresh data from the API
  console.log("Refreshing orders...");
  // For now, just show a message
  alert("Orders refreshed!");
};

onMounted(() => {
  console.log("Admin orders management loaded");
});
</script>
