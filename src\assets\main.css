@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles with larger fonts */
html {
  font-family: "Inter", sans-serif;
  font-size: 18px; /* Larger base font */
}

body {
  @apply bg-gray-50 text-gray-900 antialiased;
  line-height: 1.6;
}

/* Component styles with larger fonts */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 text-lg rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.btn-secondary {
  @apply bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 font-semibold py-4 px-8 text-lg rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.input-field {
  @apply w-full px-5 py-4 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white;
}

.card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl;
}

/* Larger text classes */
.text-larger {
  @apply text-lg;
}

.nav-text {
  @apply text-lg;
}
