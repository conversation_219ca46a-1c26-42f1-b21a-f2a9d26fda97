@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles with larger fonts */
@layer base {
  html {
    font-family: "Inter", sans-serif;
    scroll-behavior: smooth;
    font-size: 18px; /* Increased base font size from default 16px */
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    font-size: 1rem; /* 18px base */
    line-height: 1.6; /* Better line spacing */
  }

  * {
    @apply border-gray-200;
  }

  /* Increase heading sizes */
  h1 {
    @apply text-4xl font-bold; /* Larger h1 */
  }

  h2 {
    @apply text-3xl font-semibold; /* Larger h2 */
  }

  h3 {
    @apply text-2xl font-semibold; /* Larger h3 */
  }

  h4 {
    @apply text-xl font-medium; /* Larger h4 */
  }

  /* Better paragraph spacing */
  p {
    @apply text-lg leading-relaxed; /* Larger paragraph text */
  }

  /* Larger labels */
  label {
    @apply text-base font-medium; /* Larger labels */
  }
}

/* Custom component styles with larger fonts */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 text-lg rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 font-semibold py-4 px-8 text-lg rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-4 px-8 text-lg rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .input-field {
    @apply w-full px-5 py-4 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white;
  }

  .card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl;
  }

  .card-header {
    @apply border-b border-gray-100 pb-6 mb-6;
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Table styles with larger fonts */
  .table-text {
    @apply text-base; /* Larger table text */
  }

  .table-header {
    @apply text-lg font-semibold; /* Larger table headers */
  }

  /* Navigation styles */
  .nav-link {
    @apply text-lg font-medium; /* Larger navigation links */
  }

  /* Form styles */
  .form-label {
    @apply text-lg font-medium mb-3; /* Larger form labels */
  }

  .form-input {
    @apply text-lg px-5 py-4; /* Larger form inputs */
  }
}
